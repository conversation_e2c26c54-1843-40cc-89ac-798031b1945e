import { Avatar } from "antd";

import { EditIcon } from "@/components/main/icon";
import DepartmentTree from "./departmentTree";
import MemberList from "./memberList";

function Member() {
  return (
    <div className="flex h-full flex-col">
      <div className="flex h-[48px] items-center px-6 text-base font-medium">
        成员管理
      </div>
      <div className="flex h-[52px] items-center gap-2 px-6">
        <div className="group/avatar relative">
          <Avatar
            className="border-[1.4px] border-border-1 bg-bg-green-1 text-sm font-medium text-primary-default"
            size={28}
          >
            A
          </Avatar>
          <div className="absolute left-0 top-0 z-10 hidden h-[28px] w-[28px] rounded-[28px] bg-[rgba(0,0,0,0.30)] bg-black backdrop-blur-xs group-hover/avatar:block"></div>
          <EditIcon className="absolute left-1 top-1 z-20 hidden text-[20px] text-white group-hover/avatar:block" />
        </div>

        <div className="group/name flex cursor-pointer items-center gap-2 text-sm font-medium">
          <span>AIWorks 团队</span>
          <EditIcon className="hidden text-[20px] text-text-2 group-hover/name:block" />
        </div>
      </div>
      <div className="flex min-h-0 flex-1 p-6 pt-3">
        <div className="w-[240px] p-2">
          <DepartmentTree />
        </div>
        <div className="flex-1 p-4">
          <MemberList />
        </div>
      </div>
    </div>
  );
}

export default Member;
